1. python环境使用conda，环境名称为adk, 版本为python3.12，切记
2. 进入powershell运行前，输入conda activate adk，切换到adk后再运行其它命令,切记
3. google agent deveplement kit 源文件在D:\Ready\Adk\adk-python-main\src目录下，开发新功能时，检查包名引用是否正确，切记
4. google agent deveplement kit sample 源文件在D:\Ready\Adk\adk-samples-main\python目录下，开发新功能时可用做参考，切记
5. docs\Adk目录是 google agent deveplement kit 源代码的说明文档
6. docs\Sample目录是 google agent deveplement kit sample 的说明文档
7. 说明文档要使用Markdown格式，切记
8. project\backend为后台开发源文件目录，所有后台相关的开发都保存在这里
9. project\frontend为前端开发源文件目录，所有前端相关的开发都保存在这里

重要提示：

python环境使用conda，环境名称为adk, 版本为python3.12，切记
进入powershell运行前，输入conda activate adk，切换到adk后再运行其它命令,切记
python环境使用conda，环境名称为adk, 版本为python3.12，切记
进入powershell运行前，输入conda activate adk，切换到adk后再运行其它命令,切记
python环境使用conda，环境名称为adk, 版本为python3.12，切记
进入powershell运行前，输入conda activate adk，切换到adk后再运行其它命令,切记
python环境使用conda，环境名称为adk, 版本为python3.12，切记
进入powershell运行前，输入conda activate adk，切换到adk后再运行其它命令,切记
python环境使用conda，环境名称为adk, 版本为python3.12，切记
进入powershell运行前，输入conda activate adk，切换到adk后再运行其它命令,切记

如果当前某个文件不能满足要求，那就删除它，重新写，不要添加新文件，切记，切记
如果当前某个文件不能满足要求，那就删除它，重新写，不要添加新文件，切记，切记
如果当前某个文件不能满足要求，那就删除它，重新写，不要添加新文件，切记，切记
如果当前某个文件不能满足要求，那就删除它，重新写，不要添加新文件，切记，切记
如果当前某个文件不能满足要求，那就删除它，重新写，不要添加新文件，切记，切记
如果当前某个文件不能满足要求，那就删除它，重新写，不要添加新文件，切记，切记

不要除了后端固定模块可以测试，不要启动powershell去测试前后端的功能，
不要除了后端固定模块可以测试，不要启动powershell去测试前后端的功能，
不要除了后端固定模块可以测试，不要启动powershell去测试前后端的功能，
不要除了后端固定模块可以测试，不要启动powershell去测试前后端的功能，
不要除了后端固定模块可以测试，不要启动powershell去测试前后端的功能，
不要除了后端固定模块可以测试，不要启动powershell去测试前后端的功能，

尤其不要启动前端，不要启动前端，不要启动前端，不要启动前端，不要启动前端，不要启动前端，不要启动前端

