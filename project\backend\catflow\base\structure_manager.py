#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结构管理器：
- 从struct.json配置文件加载智能体结构配置
- 支持解析嵌套结构和子结构引用
- 支持main_agent作为主协调器
- 能够根据配置生成对应的智能体组装代码
"""

import json
import os
from typing import Dict, Any, Optional, List, Union
from .agent_manager import AgentManager
from .llm_manager import LLMManager
from .tool_manager import ToolManager
from google.adk.agents.sequential_agent import SequentialAgent
from google.adk.agents.llm_agent import LlmAgent
from .loop_agent import LoopAgent

class StructureManager:
    """
    智能体结构管理器：负责从配置文件加载和创建复杂的智能体结构
    """
    
    def __init__(self, config_path: Optional[str] = None, agent_manager: Optional[AgentManager] = None, llm_manager: Optional[LLMManager] = None):
        """
        初始化结构管理器
        :param config_path: 结构配置文件路径，如果为None则使用默认路径
        :param agent_manager: 智能体管理器实例，如果为None则创建新实例
        :param llm_manager: LLM管理器实例，如果为None则创建新实例
        """
        if config_path is None:
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'struct.json')
        
        self.config_path = config_path
        self.structure_configs = self._load_config(config_path)
        
        # 初始化管理器
        self.agent_manager = agent_manager if agent_manager else AgentManager()
        self.llm_manager = llm_manager if llm_manager else LLMManager()
        
        # 缓存已创建的结构
        self._structure_cache: Dict[str, Any] = {}
        self._sub_structure_cache: Dict[str, Any] = {}
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """
        加载结构配置文件
        :param config_path: 配置文件路径
        :return: 结构配置字典
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except FileNotFoundError:
            raise ValueError(f"找不到结构配置文件：{config_path}")
        except json.JSONDecodeError:
            raise ValueError(f"结构配置文件格式错误：不是有效的JSON格式")
        
        # 校验配置文件格式
        if 'structures' not in config or not isinstance(config['structures'], dict):
            raise ValueError("配置文件格式错误：缺少'structures'字段或类型不为dict")
        
        print(f"成功加载结构配置文件：{config_path}")
        print(f"可用的结构：{', '.join(config['structures'].keys())}")
        return config
    
    def list_structures(self) -> List[str]:
        """
        获取所有可用的结构名称列表
        :return: 结构名称列表
        """
        return list(self.structure_configs['structures'].keys())
    
    def get_structure_config(self, structure_name: str) -> Dict[str, Any]:
        """
        获取指定结构的配置
        :param structure_name: 结构名称
        :return: 结构配置字典
        """
        if structure_name not in self.structure_configs['structures']:
            raise ValueError(f"结构 '{structure_name}' 不存在")
        return self.structure_configs['structures'][structure_name]
    
    async def create_structure(self, structure_name: str, use_cache: bool = True) -> Any:
        """
        创建指定的智能体结构实例
        :param structure_name: 结构名称
        :param use_cache: 是否使用缓存
        :return: 智能体结构实例（main_agent）
        """
        # 检查缓存
        if use_cache and structure_name in self._structure_cache:
            return self._structure_cache[structure_name]
        
        # 获取结构配置
        structure_config = self.get_structure_config(structure_name)
        
        # 验证结构配置：一个结构要么只能有agent_refs，要么只能有sub_structures
        agent_refs = structure_config.get('agent_refs', [])
        sub_structures = structure_config.get('sub_structures', {})
        
        if agent_refs and sub_structures:
            raise ValueError(f"结构 '{structure_name}' 不能同时包含 agent_refs 和 sub_structures")
        
        # 创建主智能体
        main_agent = await self._create_main_agent(structure_config)
        
        # 缓存结构
        if use_cache:
            self._structure_cache[structure_name] = main_agent
        
        print(f"成功创建结构：{structure_name}")
        return main_agent
    
    async def _create_main_agent(self, structure_config: Dict[str, Any]) -> Any:
        """
        创建主智能体（main_agent）
        :param structure_config: 结构配置
        :return: 智能体实例（LlmAgent或SequentialAgent等）
        """
        main_agent_config = structure_config.get('main_agent', {})
        
        if not main_agent_config:
            raise ValueError("结构配置中缺少main_agent定义")
        
        # 提取主智能体配置
        agent_name = main_agent_config.get('name', 'main_agent')
        agent_type = main_agent_config.get('type', 'coordinator')
        model_name = main_agent_config.get('model')
        instruction = main_agent_config.get('instruction', '')
        description = main_agent_config.get('description', '')
        sub_structure_refs = main_agent_config.get('sub_structure_refs', [])
        
        # 获取全局指令和生成配置
        global_instruction = structure_config.get('global_instruction', '')
        generate_content_config = structure_config.get('generate_content_config', {})
        
        # 如果有全局指令，将其与主智能体指令合并
        if global_instruction:
            if instruction:
                instruction = f"{global_instruction}\n\n{instruction}"
            else:
                instruction = global_instruction
        
        # 创建子结构
        sub_agents = []
        for sub_structure_ref in sub_structure_refs:
            sub_structure = await self._create_agent_or_structure(structure_config, sub_structure_ref)
            if sub_structure:
                sub_agents.append(sub_structure)
        
        # 根据类型创建主智能体
        if agent_type == 'coordinator':
            # coordinator类型需要模型和指令
            if not model_name:
                raise ValueError("coordinator类型的main_agent配置中缺少model字段")
            
            # 从LLM管理器获取模型
            try:
                model = self.llm_manager.get_model(model_name)
                
                # 应用生成配置到模型（如果模型支持）
                if generate_content_config and hasattr(model, 'update_config'):
                    model.update_config(generate_content_config)
                elif generate_content_config:
                    print(f"注意：模型 {model_name} 不支持动态配置更新，generate_content_config 将被忽略")
                    
            except Exception as e:
                raise ValueError(f"无法获取模型 '{model_name}'：{e}")
            
            # 创建LlmAgent类型的主智能体
            main_agent = LlmAgent(
                name=agent_name,
                model=model,
                instruction=instruction,
                sub_agents=sub_agents,
                disallow_transfer_to_peers=False,
                disallow_transfer_to_parent=False
            )
            print(f"成功创建coordinator类型主智能体：{agent_name}，包含{len(sub_agents)}个子智能体")
            
        elif agent_type == 'sequential_agent':
            # sequential_agent类型使用SequentialAgent
            main_agent = SequentialAgent(
                name=agent_name,
                sub_agents=sub_agents
            )
            print(f"成功创建sequential_agent类型主智能体：{agent_name}，包含{len(sub_agents)}个子智能体")
            
        elif agent_type == 'concurrent_agent':
            # concurrent_agent类型暂时使用SequentialAgent（可以后续扩展为真正的并发实现）
            main_agent = SequentialAgent(
                name=agent_name,
                sub_agents=sub_agents
            )
            print(f"成功创建concurrent_agent类型主智能体：{agent_name}，包含{len(sub_agents)}个子智能体")
            
        elif agent_type == 'loop_agent':
            # loop_agent类型使用LoopAgent
            max_iterations = main_agent_config.get('max_iterations', None)
            main_agent = LoopAgent(
                name=agent_name,
                max_iterations=max_iterations,
                sub_agents=sub_agents
            )
            print(f"成功创建loop_agent类型主智能体：{agent_name}，包含{len(sub_agents)}个子智能体，最大循环次数：{max_iterations}")
            
        else:
            # 默认使用coordinator类型
            if not model_name:
                raise ValueError(f"未知类型 '{agent_type}' 的main_agent配置中缺少model字段")
            
            try:
                model = self.llm_manager.get_model(model_name)
                
                if generate_content_config and hasattr(model, 'update_config'):
                    model.update_config(generate_content_config)
                elif generate_content_config:
                    print(f"注意：模型 {model_name} 不支持动态配置更新，generate_content_config 将被忽略")
                    
            except Exception as e:
                raise ValueError(f"无法获取模型 '{model_name}'：{e}")
            
            main_agent = LlmAgent(
                name=agent_name,
                model=model,
                instruction=instruction,
                sub_agents=sub_agents,
                disallow_transfer_to_peers=False,
                disallow_transfer_to_parent=False
            )
            print(f"成功创建默认coordinator类型主智能体：{agent_name}，包含{len(sub_agents)}个子智能体")
        
        # 输出配置应用信息
        if global_instruction:
            print(f"已应用全局指令到主智能体")
        if generate_content_config and agent_type == 'coordinator':
            print(f"已应用生成配置：{generate_content_config}")
        
        return main_agent
    
    async def _create_agent_or_structure(self, structure_config: Dict[str, Any], agent_ref: str) -> Any:
        """
        创建智能体或子结构
        :param structure_config: 结构配置
        :param agent_ref: 智能体引用名称
        :return: 智能体或子结构实例
        """
        # 首先检查是否是子结构引用
        sub_structures = structure_config.get('sub_structures', {})
        if agent_ref in sub_structures:
            return await self._create_sub_structure(structure_config, agent_ref)
        
        # 否则尝试从agent_manager创建智能体
        try:
            agent = await self.agent_manager.create_agent(agent_ref)
            print(f"成功创建智能体：{agent_ref}")
            return agent
        except Exception as e:
            print(f"警告：无法创建智能体 '{agent_ref}'：{e}")
            return None
    
    async def _create_sub_structure(self, structure_config: Dict[str, Any], sub_structure_name: str) -> Any:
        """
        创建子结构实例
        :param structure_config: 父结构配置
        :param sub_structure_name: 子结构名称
        :return: 子结构实例
        """
        # 检查子结构缓存
        cache_key = f"{structure_config.get('name', 'unknown')}_{sub_structure_name}"
        if cache_key in self._sub_structure_cache:
            return self._sub_structure_cache[cache_key]
        
        sub_structures = structure_config.get('sub_structures', {})
        if sub_structure_name not in sub_structures:
            raise ValueError(f"子结构 '{sub_structure_name}' 不存在")
        
        sub_structure_config = sub_structures[sub_structure_name]
        sub_structure_type = sub_structure_config.get('type', 'sequential')
        sub_structure_name_actual = sub_structure_config.get('name', sub_structure_name)
        agent_refs = sub_structure_config.get('agent_refs', [])
        sub_structure_refs = sub_structure_config.get('sub_structure_refs', [])
        
        # 验证配置：一个子结构要么只能有agent_refs，要么只能有sub_structure_refs
        if agent_refs and sub_structure_refs:
            raise ValueError(f"子结构 '{sub_structure_name}' 不能同时包含 agent_refs 和 sub_structure_refs")
        
        # 创建子结构中的智能体或子结构
        sub_agents = []
        
        # 处理agent_refs
        for agent_ref in agent_refs:
            # 只创建智能体，不创建子结构
            try:
                agent = await self.agent_manager.create_agent(agent_ref)
                if agent:
                    sub_agents.append(agent)
                    print(f"成功创建智能体：{agent_ref}")
            except Exception as e:
                print(f"警告：无法创建智能体 '{agent_ref}'：{e}")
        
        # 处理sub_structure_refs
        for sub_structure_ref in sub_structure_refs:
            # 递归创建子结构
            sub_structure = await self._create_agent_or_structure(structure_config, sub_structure_ref)
            if sub_structure:
                sub_agents.append(sub_structure)
        
        # 根据类型创建子结构
        if sub_structure_type == 'coordinator':
            # 协调器类型使用LlmAgent，需要模型和指令
            # 获取全局指令和生成配置
            global_instruction = structure_config.get('global_instruction', '')
            generate_content_config = structure_config.get('generate_content_config', {})
            
            # 为coordinator类型的子结构获取模型（使用主智能体的模型或默认模型）
            main_agent_config = structure_config.get('main_agent', {})
            model_name = main_agent_config.get('model', 'qwen-max')  # 使用主智能体的模型作为默认
            
            try:
                model = self.llm_manager.get_model(model_name)
                
                # 应用生成配置到模型（如果模型支持）
                if generate_content_config and hasattr(model, 'update_config'):
                    model.update_config(generate_content_config)
                    
            except Exception as e:
                print(f"警告：无法为coordinator子结构获取模型 '{model_name}'：{e}，将使用SequentialAgent")
                # 如果无法获取模型，降级为SequentialAgent
                sub_structure_instance = SequentialAgent(
                    name=sub_structure_name_actual,
                    sub_agents=sub_agents
                )
            else:
                # 构建coordinator的指令
                coordinator_instruction = sub_structure_config.get('description', '')
                if global_instruction:
                    if coordinator_instruction:
                        coordinator_instruction = f"{global_instruction}\n\n{coordinator_instruction}"
                    else:
                        coordinator_instruction = global_instruction
                
                # 创建LlmAgent类型的coordinator
                sub_structure_instance = LlmAgent(
                    name=sub_structure_name_actual,
                    model=model,
                    instruction=coordinator_instruction,
                    sub_agents=sub_agents
                )
                print(f"coordinator子结构 '{sub_structure_name_actual}' 使用LlmAgent类型")
        
        elif sub_structure_type == 'sequential':
            sub_structure_instance = SequentialAgent(
                name=sub_structure_name_actual,
                sub_agents=sub_agents
            )
        elif sub_structure_type == 'concurrent':
            # 并发类型暂时使用SequentialAgent
            sub_structure_instance = SequentialAgent(
                name=sub_structure_name_actual,
                sub_agents=sub_agents
            )
        elif sub_structure_type == 'loop':
            # 循环类型使用LoopAgent
            max_iterations = sub_structure_config.get('max_iterations', None)
            sub_structure_instance = LoopAgent(
                name=sub_structure_name_actual,
                max_iterations=max_iterations,
                sub_agents=sub_agents
            )
            print(f"loop子结构 '{sub_structure_name_actual}' 使用LoopAgent类型，最大循环次数：{max_iterations}")
        else:
            # 默认使用SequentialAgent
            sub_structure_instance = SequentialAgent(
                name=sub_structure_name_actual,
                sub_agents=sub_agents
            )
        
        # 缓存子结构
        self._sub_structure_cache[cache_key] = sub_structure_instance
        
        print(f"成功创建子结构：{sub_structure_name_actual}（类型：{sub_structure_type}），包含{len(sub_agents)}个子智能体")
        return sub_structure_instance
    
    def clear_cache(self) -> None:
        """
        清空结构缓存
        """
        self._structure_cache.clear()
        self._sub_structure_cache.clear()
        print("结构缓存已清空")
    
    def generate_code_from_structure(self, structure_name: str) -> str:
        """
        根据结构配置生成对应的Python代码
        :param structure_name: 结构名称
        :return: 生成的Python代码字符串
        """
        structure_config = self.get_structure_config(structure_name)
        
        code_lines = []
        code_lines.append("# 自动生成的智能体结构代码")
        code_lines.append("from google.adk.agents.sequential_agent import SequentialAgent")
        code_lines.append("from google.adk.agents.llm_agent import LlmAgent")
        code_lines.append("from .loop_agent import LoopAgent")
        code_lines.append("")
        
        # 生成智能体创建代码
        agent_refs = structure_config.get('agent_refs', [])
        for agent_ref in agent_refs:
            code_lines.append(f"{agent_ref.lower().replace('agent', '_agent')} = await agent_manager.create_agent('{agent_ref}')")
        
        code_lines.append("")
        
        # 生成子结构代码（递归生成）
        sub_structures = structure_config.get('sub_structures', {})
        self._generate_sub_structure_code(code_lines, sub_structures, structure_config)
        
        # 生成主智能体代码
        main_agent_config = structure_config.get('main_agent', {})
        main_name = main_agent_config.get('name', 'main_agent')
        main_type = main_agent_config.get('type', 'coordinator')
        model_name = main_agent_config.get('model', 'default_model')
        instruction = main_agent_config.get('instruction', '')
        sub_structure_refs = main_agent_config.get('sub_structure_refs', [])
        
        # 获取全局配置
        global_instruction = structure_config.get('global_instruction', '')
        generate_content_config = structure_config.get('generate_content_config', {})
        
        # 处理全局指令
        if global_instruction:
            if instruction:
                final_instruction = f"{global_instruction}\\n\\n{instruction}"
            else:
                final_instruction = global_instruction
        else:
            final_instruction = instruction
        
        # 生成子结构列表
        sub_agent_names = []
        for sub_structure_ref in sub_structure_refs:
            if sub_structure_ref in sub_structures:
                sub_agent_names.append(sub_structure_ref)
            else:
                sub_agent_names.append(sub_structure_ref.lower().replace('agent', '_agent'))
        
        code_lines.append(f"# 创建主智能体（类型：{main_type}）")
        
        if main_type == 'coordinator':
            # coordinator类型需要模型和指令
            code_lines.append(f"# 获取模型")
            code_lines.append(f"{main_name}_model = llm_manager.get_model('{model_name}')")
            
            # 生成配置应用代码
            if generate_content_config:
                code_lines.append(f"")
                code_lines.append(f"# 应用生成配置")
                code_lines.append(f"generate_config = {generate_content_config}")
                code_lines.append(f"if hasattr({main_name}_model, 'update_config'):")
                code_lines.append(f"    {main_name}_model.update_config(generate_config)")
            
            code_lines.append("")
            code_lines.append(f"{main_name} = LlmAgent(")
            code_lines.append(f"    name=\"{main_name}\",")
            code_lines.append(f"    model={main_name}_model,")
            code_lines.append(f"    instruction=\"{final_instruction}\",")
            code_lines.append(f"    sub_agents=[{', '.join(sub_agent_names)}]")
            code_lines.append(")")
            
        elif main_type == 'sequential_agent':
            # 顺序类型使用SequentialAgent
            code_lines.append(f"{main_name} = SequentialAgent(")
            code_lines.append(f"    name=\"{main_name}\",")
            code_lines.append(f"    sub_agents=[{', '.join(sub_agent_names)}]")
            code_lines.append(")")
            
        elif main_type == 'concurrent_agent':
            # 并发类型暂时使用SequentialAgent
            code_lines.append(f"{main_name} = SequentialAgent(")
            code_lines.append(f"    name=\"{main_name}\",")
            code_lines.append(f"    sub_agents=[{', '.join(sub_agent_names)}]")
            code_lines.append(")")
            
        elif main_type == 'loop_agent':
            # 循环类型使用LoopAgent
            max_iterations = main_agent_config.get('max_iterations', None)
            code_lines.append(f"{main_name} = LoopAgent(")
            code_lines.append(f"    name=\"{main_name}\",")
            if max_iterations is not None:
                code_lines.append(f"    max_iterations={max_iterations},")
            code_lines.append(f"    sub_agents=[{', '.join(sub_agent_names)}]")
            code_lines.append(")")
            
        else:
            # 默认使用coordinator类型
            code_lines.append(f"# 未知类型 '{main_type}'，使用默认coordinator类型")
            code_lines.append(f"# 获取模型")
            code_lines.append(f"{main_name}_model = llm_manager.get_model('{model_name}')")
            
            if generate_content_config:
                code_lines.append(f"")
                code_lines.append(f"# 应用生成配置")
                code_lines.append(f"generate_config = {generate_content_config}")
                code_lines.append(f"if hasattr({main_name}_model, 'update_config'):")
                code_lines.append(f"    {main_name}_model.update_config(generate_config)")
            
            code_lines.append("")
            code_lines.append(f"{main_name} = LlmAgent(")
            code_lines.append(f"    name=\"{main_name}\",")
            code_lines.append(f"    model={main_name}_model,")
            code_lines.append(f"    instruction=\"{final_instruction}\",")
            code_lines.append(f"    sub_agents=[{', '.join(sub_agent_names)}]")
            code_lines.append(")")
        
        return "\n".join(code_lines)
    
    def _generate_sub_structure_code(self, code_lines: List[str], sub_structures: Dict[str, Any], structure_config: Dict[str, Any]) -> None:
        """
        递归生成子结构代码
        :param code_lines: 代码行列表
        :param sub_structures: 子结构配置
        :param structure_config: 完整结构配置
        """
        # 获取全局配置
        global_instruction = structure_config.get('global_instruction', '')
        generate_content_config = structure_config.get('generate_content_config', {})
        main_agent_config = structure_config.get('main_agent', {})
        model_name = main_agent_config.get('model', 'qwen-max')
        
        for sub_name, sub_config in sub_structures.items():
            sub_type = sub_config.get('type', 'sequential')
            agent_refs = sub_config.get('agent_refs', [])
            description = sub_config.get('description', '')
            
            # 生成子结构列表
            sub_agent_names = []
            for agent_ref in agent_refs:
                if agent_ref in sub_structures:
                    # 如果引用的是另一个子结构，直接使用名称
                    sub_agent_names.append(agent_ref)
                else:
                    # 如果引用的是普通智能体，转换名称
                    sub_agent_names.append(agent_ref.lower().replace('agent', '_agent'))
            
            code_lines.append(f"# 创建子结构：{sub_name}（类型：{sub_type}）")
            
            if sub_type == 'coordinator':
                # coordinator类型使用LlmAgent
                code_lines.append(f"{sub_name}_model = llm_manager.get_model('{model_name}')")
                
                # 处理coordinator的指令
                if global_instruction:
                    if description:
                        coordinator_instruction = f"{global_instruction}\\n\\n{description}"
                    else:
                        coordinator_instruction = global_instruction
                else:
                    coordinator_instruction = description
                
                # 生成配置应用代码（如果有）
                if generate_content_config:
                    code_lines.append(f"if hasattr({sub_name}_model, 'update_config'):")
                    code_lines.append(f"    {sub_name}_model.update_config({generate_content_config})")
                
                code_lines.append(f"{sub_name} = LlmAgent(")
                code_lines.append(f"    name=\"{sub_name}\",")
                code_lines.append(f"    model={sub_name}_model,")
                code_lines.append(f"    instruction=\"{coordinator_instruction}\",")
                code_lines.append(f"    sub_agents=[{', '.join(sub_agent_names)}]")
                code_lines.append(")")
            elif sub_type == 'loop':
                # 循环类型使用LoopAgent
                max_iterations = sub_config.get('max_iterations', None)
                code_lines.append(f"{sub_name} = LoopAgent(")
                code_lines.append(f"    name=\"{sub_name}\",")
                if max_iterations is not None:
                    code_lines.append(f"    max_iterations={max_iterations},")
                code_lines.append(f"    sub_agents=[{', '.join(sub_agent_names)}]")
                code_lines.append(")")
            else:
                # 其他类型使用SequentialAgent
                code_lines.append(f"{sub_name} = SequentialAgent(")
                code_lines.append(f"    name=\"{sub_name}\",")
                code_lines.append(f"    sub_agents=[{', '.join(sub_agent_names)}]")
                code_lines.append(")")
            
            code_lines.append("")
    
    def get_structure_info(self, structure_name: str) -> Dict[str, Any]:
        """
        获取结构的详细信息
        :param structure_name: 结构名称
        :return: 结构信息字典
        """
        structure_config = self.get_structure_config(structure_name)
        
        info = {
            'name': structure_config.get('name', structure_name),
            'description': structure_config.get('description', ''),
            'type': structure_config.get('type', ''),
            'main_agent': structure_config.get('main_agent', {}),
            'agent_refs': structure_config.get('agent_refs', []),
            'sub_structure_refs': structure_config.get('main_agent', {}).get('sub_structure_refs', []),
            'sub_structures': list(structure_config.get('sub_structures', {}).keys()),
            'sub_structure_count': len(structure_config.get('sub_structures', {})),
            'total_agent_refs': len(structure_config.get('agent_refs', [])),
            'total_sub_structure_refs': len(structure_config.get('main_agent', {}).get('sub_structure_refs', []))
        }
        
        return info