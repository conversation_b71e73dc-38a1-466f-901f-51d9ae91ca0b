{"meta": {"description": "价格对比智能体结构配置文件", "purpose": "定义价格对比系统的多智能体协作结构", "structure": {"structures": {"type": "object", "description": "结构配置对象，包含所有智能体结构的定义", "properties": {"[structure_name]": {"type": "object", "description": "单个智能体结构的配置", "properties": {"name": {"type": "string", "description": "结构名称，用于标识该结构", "required": true}, "description": {"type": "string", "description": "结构描述，说明该结构的用途和特点", "required": false}, "type": {"type": "string", "description": "结构类型，如coordinator、sequential、concurrent、loop等", "required": true, "enum": ["coordinator", "sequential", "concurrent", "loop"]}, "global_instruction": {"type": "string", "description": "全局指令，适用于整个结构的通用指导", "required": false}, "generate_content_config": {"type": "object", "description": "内容生成配置", "properties": {"temperature": {"type": "number", "description": "生成温度", "range": "0.0-1.0"}, "max_output_tokens": {"type": "integer", "description": "最大输出token数量"}}}, "main_agent": {"type": "object", "description": "主智能体配置，每个结构必须有且仅有一个主智能体", "required": true, "properties": {"name": {"type": "string", "description": "主智能体名称"}, "type": {"type": "string", "description": "主智能体类型", "enum": ["sequential_agent", "concurrent_agent", "loop_agent"]}, "model": {"type": "string", "description": "使用的语言模型，需在llm_config.json中定义"}, "max_iterations": {"type": "integer", "description": "最大迭代次数（仅适用于loop_agent类型）", "required": false}, "instruction": {"type": "string", "description": "主智能体的指令"}, "sub_structure_refs": {"type": "array", "description": "主智能体引用的子结构名称列表，用于构建复合智能体结构", "items": {"type": "string"}, "required": false}}}, "agent_refs": {"type": "array", "description": "结构中直接引用的智能体列表，需在agent_config.json中定义。注意：一个结构要么只能有agent_refs，要么只能有sub_structures，不能同时存在", "items": "string"}, "sub_structures": {"type": "object", "description": "子结构定义，用于创建嵌套的智能体结构。注意：一个结构要么只能有agent_refs，要么只能有sub_structures，不能同时存在", "properties": {"[sub_structure_name]": {"type": "object", "description": "子结构配置", "properties": {"name": {"type": "string", "description": "子结构名称"}, "type": {"type": "string", "description": "子结构类型", "enum": ["sequential", "concurrent", "loop", "sequential_agent"]}, "max_iterations": {"type": "integer", "description": "最大迭代次数（仅适用于loop类型）"}, "description": {"type": "string", "description": "子结构描述"}, "agent_refs": {"type": "array", "description": "子结构中引用的智能体列表。注意：一个子结构要么只能有agent_refs，要么只能有sub_structure_refs，不能同时存在", "items": "string"}, "sub_structure_refs": {"type": "array", "description": "子结构中引用的其他子结构列表。注意：一个子结构要么只能有agent_refs，要么只能有sub_structure_refs，不能同时存在", "items": "string"}}}}}}}}}}, "usage": {"description": "使用方法", "steps": ["1. 在structures对象中定义新的智能体结构", "2. 设置结构的基本属性：name、type、description", "3. 配置main_agent定义主智能体", "4. 在sub_structure_refs中列出主智能体需要的子结构", "5. 根据需要在sub_structures中定义子结构", "6. 设置全局指令和生成配置"], "structure_types": {"coordinator": "协调器类型，用于管理和协调多个智能体", "sequential": "顺序执行类型，智能体按顺序依次执行", "concurrent": "并发执行类型，智能体同时并行执行", "loop": "循环执行类型，智能体循环执行直到满足退出条件"}, "best_practices": ["合理设计智能体的分工和协作关系", "为循环类型设置合适的max_iterations避免无限循环", "使用描述性的名称和详细的description", "确保sub_structure_refs中的子结构都在sub_structures中定义", "根据任务特点选择合适的结构类型", "一个结构要么只能有agent_refs，要么只能有sub_structures，不能同时存在", "一个子结构要么只能有agent_refs，要么只能有sub_structure_refs，不能同时存在"]}}, "structures": {"price_comparison_structure": {"name": "价格对比结构", "description": "用于商品价格对比的多智能体协作结构，包含多层嵌套子结构", "type": "coordinator", "global_instruction": "请以专业、客观的态度进行价格对比分析，提供详细的价格信息和对比结果。所有回复都应该准确、清晰，并包含具体的价格数据和推荐建议。", "generate_content_config": {"temperature": 0.3, "max_output_tokens": 102400}, "main_agent": {"name": "price_coordinator", "type": "sequential_agent", "model": "qwen-max", "instruction": "你是一个价格对比协调器，负责协调各个子结构来完成完整的价格对比分析。按以下顺序执行：1. 数据收集阶段 2. 分析处理阶段 3. 报告生成阶段。确保每个阶段都完成后再进入下一阶段。", "description": "主智能体，负责协调整个价格对比流程", "sub_structure_refs": ["data_collection_phase", "analysis_phase", "report_phase"]}, "sub_structures": {"data_collection_phase": {"name": "data_collection_phase", "type": "concurrent", "description": "数据收集阶段，包含多个平台的数据收集子结构", "sub_structure_refs": ["ecommerce_collection", "offline_collection"]}, "ecommerce_collection": {"name": "ecommerce_collection", "type": "sequential", "description": "电商平台数据收集子结构", "agent_refs": ["taobaoAgent", "jdAgent", "tmallAgent"]}, "offline_collection": {"name": "offline_collection", "type": "sequential", "description": "线下渠道数据收集子结构", "agent_refs": ["supermarketAgent", "retailStoreAgent"]}, "analysis_phase": {"name": "analysis_phase", "type": "sequential", "description": "分析处理阶段，包含数据处理和价格分析子结构", "sub_structure_refs": ["data_processing", "price_analysis"]}, "data_processing": {"name": "data_processing", "type": "sequential", "description": "数据处理子结构", "agent_refs": ["dataCleanAgent", "dataValidationAgent"]}, "price_analysis": {"name": "price_analysis", "type": "concurrent", "description": "价格分析子结构", "agent_refs": ["priceComparisonAgent", "trendAnalysisAgent", "statisticsAgent"]}, "report_phase": {"name": "report_phase", "type": "sequential", "description": "报告生成阶段", "sub_structure_refs": ["report_generation"]}, "report_generation": {"name": "report_generation", "type": "sequential", "description": "报告生成子结构", "agent_refs": ["reportAgent", "recommendationAgent"]}}}}}