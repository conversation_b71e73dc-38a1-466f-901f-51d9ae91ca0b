{"meta": {"description": "智能体结构配置文件", "purpose": "定义多智能体系统的组织结构和协作模式", "structure": {"structures": {"type": "object", "description": "结构配置对象，包含所有智能体结构的定义", "properties": {"[structure_name]": {"type": "object", "description": "单个智能体结构的配置", "properties": {"name": {"type": "string", "description": "结构名称，用于标识该结构", "required": true}, "description": {"type": "string", "description": "结构描述，说明该结构的用途和特点", "required": false}, "type": {"type": "string", "description": "结构类型，如coordinator、sequential、concurrent、loop等", "required": true, "enum": ["coordinator", "sequential", "concurrent", "loop"]}, "global_instruction": {"type": "string", "description": "全局指令，适用于整个结构的通用指导", "required": false}, "generate_content_config": {"type": "object", "description": "内容生成配置", "properties": {"temperature": {"type": "number", "description": "生成温度", "range": "0.0-1.0"}, "max_output_tokens": {"type": "integer", "description": "最大输出token数量"}}}, "main_agent": {"type": "object", "description": "主智能体配置，每个结构必须有且仅有一个主智能体", "required": true, "properties": {"name": {"type": "string", "description": "主智能体名称"}, "type": {"type": "string", "description": "主智能体类型", "enum": ["sequential_agent", "concurrent_agent", "loop_agent"]}, "model": {"type": "string", "description": "使用的语言模型，需在llm_config.json中定义"}, "max_iterations": {"type": "integer", "description": "最大迭代次数（仅适用于loop_agent类型）", "required": false}, "instruction": {"type": "string", "description": "主智能体的指令"}, "sub_structure_refs": {"type": "array", "description": "主智能体引用的子结构名称列表，用于构建复合智能体结构", "items": {"type": "string"}, "required": false}}}, "agent_refs": {"type": "array", "description": "结构中直接引用的智能体列表，需在agent_config.json中定义。注意：一个结构要么只能有agent_refs，要么只能有sub_structures，不能同时存在", "items": "string"}, "sub_structures": {"type": "object", "description": "子结构定义，用于创建嵌套的智能体结构。注意：一个结构要么只能有agent_refs，要么只能有sub_structures，不能同时存在", "properties": {"[sub_structure_name]": {"type": "object", "description": "子结构配置", "properties": {"name": {"type": "string", "description": "子结构名称"}, "type": {"type": "string", "description": "子结构类型", "enum": ["sequential", "concurrent", "loop", "sequential_agent"]}, "max_iterations": {"type": "integer", "description": "最大迭代次数（仅适用于loop类型）"}, "description": {"type": "string", "description": "子结构描述"}, "agent_refs": {"type": "array", "description": "子结构中引用的智能体列表。注意：一个子结构要么只能有agent_refs，要么只能有sub_structure_refs，不能同时存在。当子结构包含具体的智能体时使用此字段", "items": "string", "mutually_exclusive_with": "sub_structure_refs"}, "sub_structure_refs": {"type": "array", "description": "子结构中引用的其他子结构列表。注意：一个子结构要么只能有agent_refs，要么只能有sub_structure_refs，不能同时存在。当子结构需要包含其他子结构时使用此字段", "items": "string", "mutually_exclusive_with": "agent_refs"}}}}}}}}}}, "usage": {"description": "使用方法", "steps": ["1. 在structures对象中定义新的智能体结构", "2. 设置结构的基本属性：name、type、description", "3. 配置main_agent定义主智能体", "4. 在sub_structure_refs中列出主智能体需要的子结构", "5. 根据需要在sub_structures中定义子结构", "6. 设置全局指令和生成配置"], "structure_types": {"coordinator": "协调器类型，用于管理和协调多个智能体", "sequential": "顺序执行类型，智能体按顺序依次执行", "concurrent": "并发执行类型，智能体同时并行执行", "loop": "循环执行类型，智能体循环执行直到满足退出条件"}, "best_practices": ["合理设计智能体的分工和协作关系", "为循环类型设置合适的max_iterations避免无限循环", "使用描述性的名称和详细的description", "确保sub_structure_refs中的子结构都在sub_structures中定义", "根据任务特点选择合适的结构类型", "一个结构要么只能有agent_refs，要么只能有sub_structures，不能同时存在", "一个子结构要么只能有agent_refs，要么只能有sub_structure_refs，不能同时存在", "在前端编辑器中，通过'内容类型'下拉框选择子结构包含智能体还是其他子结构", "子结构的嵌套层次不宜过深，建议不超过3层", "当子结构需要包含多个不同类型的处理阶段时，使用sub_structure_refs", "当子结构需要包含具体执行任务的智能体时，使用agent_refs"]}}, "structures": {"travel_planning_structure": {"name": "旅行规划结构", "description": "用于旅行规划的多智能体协作结构", "type": "coordinator", "global_instruction": "请以专业、友好的语调回复用户，提供详细且实用的旅行建议。所有回复都应该结构化、清晰，并包含具体的建议和选项。", "generate_content_config": {"temperature": 0.7, "max_output_tokens": 204800}, "main_agent": {"name": "main_agent", "type": "loop_agent", "model": "qwen-max", "max_iterations": 1, "instruction": "你是一个旅行计划协调器，负责生成完整的旅行计划报告。你需要协调多个专门的子agent来收集信息并生成最终报告。必须按以下顺序执行所有步骤：\n1. 首先转移到plan_agent获取计划\n2. 然后转移到loop_agent中获取天气信息\n3. 最后转移到summaryAgent里生成报告。\n确保完成所有三个步骤后生成最终报告。  ", "description": "主agent定义，每个结构下必须要有这个agent，且只能有一个,且这个agent不可从agent_manager.py中获取，它是LlmAgent类型，model:参数代表模型名称，只从llm_manager.py中获取，不另写代码", "sub_structure_refs": ["plan_agent", "sequential_agent", "summary_agent"]}, "sub_structures": {"sequential_agent": {"name": "sequential_agent", "type": "sequential", "description": "包含天气智能体的顺序结构", "agent_refs": ["weatherAgent", "restaurantAgent", "hotelAgent"]}, "plan_agent": {"name": "plan_agent", "type": "sequential", "description": "包含旅行计划智能体的顺序结构", "agent_refs": ["planTravelAgent"]}, "summary_agent": {"name": "summary_agent", "type": "sequential", "description": "生成最终旅行计划报告的智能体结构", "agent_refs": ["contextAgent", "summaryAgent"]}}}}}